/**
 * 翻译默认目标语言配置示例
 * 展示如何在 aiChatConfig 中获取和使用 translationDefaultTargetLanguage
 */

import { createComposerConfig, updateComposerConfigWithUserSettings } from '../config/aiChatConfig'

// 示例：立即创建默认配置
export const createDefaultConfig = () => {
  console.log('创建默认配置，使用中文作为默认翻译目标语言')
  
  const defaultConfig = createComposerConfig({}, 'zh')
  
  // 查看翻译配置
  const translateSkill = defaultConfig.skill[0]
  const aiTranslateChild = translateSkill.children?.[0]
  
  console.log('默认翻译目标语言:', aiTranslateChild?.translateOptions?.tgt_lang?.defaultValue)
  
  return defaultConfig
}

// 示例：异步更新为用户配置
export const updateWithUserConfig = async () => {
  console.log('异步更新为用户配置的翻译目标语言')
  
  try {
    const updatedConfig = await updateComposerConfigWithUserSettings({})
    
    // 查看更新后的翻译配置
    const translateSkill = updatedConfig.skill[0]
    const aiTranslateChild = translateSkill.children?.[0]
    
    console.log('用户配置的翻译目标语言:', aiTranslateChild?.translateOptions?.tgt_lang?.defaultValue)
    
    return updatedConfig
  } catch (error) {
    console.error('更新配置失败:', error)
    // 返回默认配置
    return createDefaultConfig()
  }
}

// 示例：模拟 React 组件中的使用方式
export const simulateReactComponentUsage = () => {
  console.log('模拟 React 组件中的使用方式')
  
  // 1. 立即创建默认配置，不阻塞渲染
  let composerConfig = createComposerConfig({}, 'zh')
  console.log('步骤1: 立即渲染，使用默认配置')
  
  // 2. 异步更新配置
  updateComposerConfigWithUserSettings({})
    .then(updatedConfig => {
      composerConfig = updatedConfig
      console.log('步骤2: 异步更新完成，使用用户配置')
    })
    .catch(error => {
      console.error('步骤2: 更新失败，继续使用默认配置', error)
    })
  
  return composerConfig
}

// 运行示例（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('=== 翻译配置示例 ===')
  
  // 示例1：默认配置
  createDefaultConfig()
  
  // 示例2：异步更新
  updateWithUserConfig()
  
  // 示例3：模拟组件使用
  simulateReactComponentUsage()
}
